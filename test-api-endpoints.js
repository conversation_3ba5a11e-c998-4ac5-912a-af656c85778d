const axios = require('axios');

// API Configuration
const API_BASE_URL = 'http://192.168.1.15:3000/api';
const TEST_USER = {
  username: 'admin',
  password: 'admin123'
};

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Test results storage
let testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

// Helper function to log test results
function logTest(endpoint, method, status, message, error = null) {
  testResults.total++;
  const success = status === 'PASS';
  if (success) {
    testResults.passed++;
  } else {
    testResults.failed++;
  }
  
  const color = success ? colors.green : colors.red;
  const statusText = success ? '✓ PASS' : '✗ FAIL';
  
  console.log(`${color}${statusText}${colors.reset} ${colors.bold}${method}${colors.reset} ${endpoint} - ${message}`);
  if (error) {
    console.log(`  ${colors.red}Error: ${error}${colors.reset}`);
  }
  
  testResults.details.push({
    endpoint,
    method,
    status,
    message,
    error
  });
}

// Helper function to make API requests
async function makeRequest(method, endpoint, data = null, headers = {}) {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data?.message || error.message,
      status: error.response?.status || 500
    };
  }
}

// Test functions
async function testHealthCheck() {
  console.log(`\n${colors.blue}${colors.bold}=== Testing Health Check ===${colors.reset}`);
  
  const result = await makeRequest('GET', '/health');
  if (result.success) {
    logTest('/health', 'GET', 'PASS', `Server status: ${result.data.data?.server}, DB: ${result.data.data?.database}`);
  } else {
    logTest('/health', 'GET', 'FAIL', 'Health check failed', result.error);
  }
}

async function testAuthEndpoints() {
  console.log(`\n${colors.blue}${colors.bold}=== Testing Auth Endpoints ===${colors.reset}`);
  
  // Test login
  const loginResult = await makeRequest('POST', '/auth/login', TEST_USER);
  if (loginResult.success && loginResult.data.data?.token) {
    logTest('/auth/login', 'POST', 'PASS', 'Login successful');
    return loginResult.data.data.token;
  } else {
    logTest('/auth/login', 'POST', 'FAIL', 'Login failed', loginResult.error);
    return null;
  }
}

async function testProfileEndpoints(token) {
  console.log(`\n${colors.blue}${colors.bold}=== Testing Profile Endpoints ===${colors.reset}`);
  
  const headers = { Authorization: `Bearer ${token}` };
  
  // Test get profiles list
  const listResult = await makeRequest('GET', '/profiles?page=1&limit=5', null, headers);
  if (listResult.success) {
    logTest('/profiles', 'GET', 'PASS', `Retrieved ${listResult.data.data?.profiles?.length || 0} profiles`);
  } else {
    logTest('/profiles', 'GET', 'FAIL', 'Failed to get profiles', listResult.error);
  }
  
  // Test create profile
  const newProfile = {
    nama: 'Test Profile API',
    usia: 65,
    alamat: 'Test Address',
    no_telepon: '081234567890',
    kontak_darurat: '081234567891',
    riwayat_penyakit: 'Diabetes',
    obat_rutin: 'Metformin',
    alergi: 'None'
  };
  
  const createResult = await makeRequest('POST', '/profiles', newProfile, headers);
  if (createResult.success) {
    logTest('/profiles', 'POST', 'PASS', `Profile created with ID: ${createResult.data.data?.profile?.id}`);
    return createResult.data.data?.profile?.id;
  } else {
    logTest('/profiles', 'POST', 'FAIL', 'Failed to create profile', createResult.error);
    return null;
  }
}

async function testCheckupEndpoints(token, profileId) {
  console.log(`\n${colors.blue}${colors.bold}=== Testing Checkup Endpoints ===${colors.reset}`);
  
  const headers = { Authorization: `Bearer ${token}` };
  
  // Test get checkups list
  const listResult = await makeRequest('GET', '/checkups?page=1&limit=5', null, headers);
  if (listResult.success) {
    logTest('/checkups', 'GET', 'PASS', `Retrieved ${listResult.data.data?.checkups?.length || 0} checkups`);
  } else {
    logTest('/checkups', 'GET', 'FAIL', 'Failed to get checkups', listResult.error);
  }
  
  // Test create checkup (if we have a profile ID)
  if (profileId) {
    const newCheckup = {
      profile_id: profileId,
      tekanan_darah: '120/80',
      gula_darah: 110,
      berat_badan: 65,
      tinggi_badan: 160,
      keluhan: 'Test checkup via API',
      tanggal: new Date().toISOString().split('T')[0],
      catatan: 'API test checkup'
    };
    
    const createResult = await makeRequest('POST', '/checkups', newCheckup, headers);
    if (createResult.success) {
      logTest('/checkups', 'POST', 'PASS', `Checkup created with ID: ${createResult.data.data?.checkup?.id}`);
      return createResult.data.data?.checkup?.id;
    } else {
      logTest('/checkups', 'POST', 'FAIL', 'Failed to create checkup', createResult.error);
    }
  }
  
  return null;
}

async function testDashboardEndpoints(token) {
  console.log(`\n${colors.blue}${colors.bold}=== Testing Dashboard Endpoints ===${colors.reset}`);
  
  const headers = { Authorization: `Bearer ${token}` };
  
  // Test dashboard stats
  const statsResult = await makeRequest('GET', '/dashboard/stats', null, headers);
  if (statsResult.success) {
    const stats = statsResult.data.data?.overview;
    logTest('/dashboard/stats', 'GET', 'PASS', `Stats: ${stats?.totalProfiles} profiles, ${stats?.todayCheckups} checkups today`);
  } else {
    logTest('/dashboard/stats', 'GET', 'FAIL', 'Failed to get dashboard stats', statsResult.error);
  }
  
  // Test dashboard alerts
  const alertsResult = await makeRequest('GET', '/dashboard/alerts', null, headers);
  if (alertsResult.success) {
    const alertCount = alertsResult.data.data?.summary?.total || 0;
    logTest('/dashboard/alerts', 'GET', 'PASS', `Retrieved ${alertCount} alerts`);
  } else {
    logTest('/dashboard/alerts', 'GET', 'FAIL', 'Failed to get dashboard alerts', alertsResult.error);
  }
  
  // Test dashboard reports
  const reportsResult = await makeRequest('GET', '/dashboard/reports?period=month', null, headers);
  if (reportsResult.success) {
    logTest('/dashboard/reports', 'GET', 'PASS', 'Reports retrieved successfully');
  } else {
    logTest('/dashboard/reports', 'GET', 'FAIL', 'Failed to get dashboard reports', reportsResult.error);
  }
}

// Main test function
async function runAllTests() {
  console.log(`${colors.bold}${colors.blue}🚀 Starting API Endpoint Tests${colors.reset}`);
  console.log(`${colors.yellow}Testing API at: ${API_BASE_URL}${colors.reset}\n`);
  
  try {
    // Test health check first
    await testHealthCheck();
    
    // Test authentication and get token
    const token = await testAuthEndpoints();
    
    if (!token) {
      console.log(`\n${colors.red}${colors.bold}❌ Cannot proceed without authentication token${colors.reset}`);
      return;
    }
    
    // Test other endpoints with authentication
    const profileId = await testProfileEndpoints(token);
    await testCheckupEndpoints(token, profileId);
    await testDashboardEndpoints(token);
    
    // Test additional auth endpoints
    console.log(`\n${colors.blue}${colors.bold}=== Testing Additional Auth Endpoints ===${colors.reset}`);
    const headers = { Authorization: `Bearer ${token}` };
    
    const verifyResult = await makeRequest('POST', '/auth/verify-token', null, headers);
    if (verifyResult.success) {
      logTest('/auth/verify-token', 'POST', 'PASS', 'Token verification successful');
    } else {
      logTest('/auth/verify-token', 'POST', 'FAIL', 'Token verification failed', verifyResult.error);
    }
    
  } catch (error) {
    console.log(`\n${colors.red}${colors.bold}❌ Test execution failed: ${error.message}${colors.reset}`);
  }
  
  // Print summary
  printSummary();
}

function printSummary() {
  console.log(`\n${colors.bold}${colors.blue}📊 Test Summary${colors.reset}`);
  console.log(`${colors.bold}Total Tests: ${testResults.total}${colors.reset}`);
  console.log(`${colors.green}${colors.bold}Passed: ${testResults.passed}${colors.reset}`);
  console.log(`${colors.red}${colors.bold}Failed: ${testResults.failed}${colors.reset}`);
  
  const successRate = testResults.total > 0 ? ((testResults.passed / testResults.total) * 100).toFixed(1) : 0;
  console.log(`${colors.bold}Success Rate: ${successRate}%${colors.reset}`);
  
  if (testResults.failed > 0) {
    console.log(`\n${colors.red}${colors.bold}❌ Failed Tests:${colors.reset}`);
    testResults.details
      .filter(test => test.status === 'FAIL')
      .forEach(test => {
        console.log(`  ${colors.red}• ${test.method} ${test.endpoint}: ${test.error}${colors.reset}`);
      });
  }
  
  console.log(`\n${colors.bold}${colors.blue}🏁 Test execution completed${colors.reset}`);
}

// Test specific profile endpoints
async function testSpecificProfileEndpoints(token, profileId) {
  if (!profileId) return;

  console.log(`\n${colors.blue}${colors.bold}=== Testing Specific Profile Endpoints ===${colors.reset}`);
  const headers = { Authorization: `Bearer ${token}` };

  // Test get specific profile
  const getResult = await makeRequest('GET', `/profiles/${profileId}`, null, headers);
  if (getResult.success) {
    logTest(`/profiles/${profileId}`, 'GET', 'PASS', 'Profile retrieved successfully');
  } else {
    logTest(`/profiles/${profileId}`, 'GET', 'FAIL', 'Failed to get profile', getResult.error);
  }

  // Test get profile QR
  const qrResult = await makeRequest('GET', `/profiles/${profileId}/qr`, null, headers);
  if (qrResult.success) {
    logTest(`/profiles/${profileId}/qr`, 'GET', 'PASS', 'QR code retrieved successfully');
  } else {
    logTest(`/profiles/${profileId}/qr`, 'GET', 'FAIL', 'Failed to get QR code', qrResult.error);
  }

  // Test update profile
  const updateData = { alamat: 'Updated Address via API' };
  const updateResult = await makeRequest('PUT', `/profiles/${profileId}`, updateData, headers);
  if (updateResult.success) {
    logTest(`/profiles/${profileId}`, 'PUT', 'PASS', 'Profile updated successfully');
  } else {
    logTest(`/profiles/${profileId}`, 'PUT', 'FAIL', 'Failed to update profile', updateResult.error);
  }
}

// Test QR scan endpoint
async function testQRScanEndpoint(token) {
  console.log(`\n${colors.blue}${colors.bold}=== Testing QR Scan Endpoint ===${colors.reset}`);
  const headers = { Authorization: `Bearer ${token}` };

  // Test QR scan with dummy data
  const scanData = { qr_data: 'LANSIA_QR_12345' };
  const scanResult = await makeRequest('POST', '/profiles/scan', scanData, headers);
  if (scanResult.success) {
    logTest('/profiles/scan', 'POST', 'PASS', 'QR scan successful');
  } else {
    logTest('/profiles/scan', 'POST', 'FAIL', 'QR scan failed', scanResult.error);
  }
}

// Test error endpoints (should return 404)
async function testErrorEndpoints(token) {
  console.log(`\n${colors.blue}${colors.bold}=== Testing Error Handling ===${colors.reset}`);
  const headers = { Authorization: `Bearer ${token}` };

  // Test non-existent endpoint
  const notFoundResult = await makeRequest('GET', '/nonexistent', null, headers);
  if (notFoundResult.status === 404) {
    logTest('/nonexistent', 'GET', 'PASS', '404 error handled correctly');
  } else {
    logTest('/nonexistent', 'GET', 'FAIL', 'Expected 404 error', `Got status: ${notFoundResult.status}`);
  }

  // Test non-existent profile
  const badProfileResult = await makeRequest('GET', '/profiles/99999', null, headers);
  if (badProfileResult.status === 404) {
    logTest('/profiles/99999', 'GET', 'PASS', 'Non-existent profile handled correctly');
  } else {
    logTest('/profiles/99999', 'GET', 'FAIL', 'Expected 404 for non-existent profile', `Got status: ${badProfileResult.status}`);
  }
}

// Enhanced main test function
async function runAllTests() {
  console.log(`${colors.bold}${colors.blue}🚀 Starting Comprehensive API Endpoint Tests${colors.reset}`);
  console.log(`${colors.yellow}Testing API at: ${API_BASE_URL}${colors.reset}\n`);

  try {
    // Test health check first
    await testHealthCheck();

    // Test authentication and get token
    const token = await testAuthEndpoints();

    if (!token) {
      console.log(`\n${colors.red}${colors.bold}❌ Cannot proceed without authentication token${colors.reset}`);
      return;
    }

    // Test other endpoints with authentication
    const profileId = await testProfileEndpoints(token);
    const checkupId = await testCheckupEndpoints(token, profileId);
    await testDashboardEndpoints(token);

    // Test specific profile endpoints
    await testSpecificProfileEndpoints(token, profileId);

    // Test QR scan
    await testQRScanEndpoint(token);

    // Test additional auth endpoints
    console.log(`\n${colors.blue}${colors.bold}=== Testing Additional Auth Endpoints ===${colors.reset}`);
    const headers = { Authorization: `Bearer ${token}` };

    const verifyResult = await makeRequest('POST', '/auth/verify-token', null, headers);
    if (verifyResult.success) {
      logTest('/auth/verify-token', 'POST', 'PASS', 'Token verification successful');
    } else {
      logTest('/auth/verify-token', 'POST', 'FAIL', 'Token verification failed', verifyResult.error);
    }

    // Test error handling
    await testErrorEndpoints(token);

  } catch (error) {
    console.log(`\n${colors.red}${colors.bold}❌ Test execution failed: ${error.message}${colors.reset}`);
  }

  // Print summary
  printSummary();
}

// Run the tests
if (require.main === module) {
  runAllTests().catch(error => {
    console.error(`${colors.red}${colors.bold}Fatal error: ${error.message}${colors.reset}`);
    process.exit(1);
  });
}
