# API Endpoints Documentation - Aplik<PERSON> Kesehatan Lansia

## Base URL
- **Development**: `http://192.168.1.15:3000/api`
- **Production**: `https://your-production-api.com/api`

## Authentication
Most endpoints require JWT token authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

---

## 🏥 Health Check

### GET /health
Check server and database status
- **Access**: Public
- **Response**: Server status, database connection, timestamp, version

---

## 🔐 Authentication Endpoints

### POST /auth/login
User login
- **Access**: Public
- **Body**: `{ username, password }`
- **Response**: JWT token and user data
- **Rate Limited**: Yes

### POST /auth/register
Register new user (Admin only)
- **Access**: Private (Admin)
- **Body**: `{ username, password, posyandu_name, pin, role }`
- **Response**: New user data

### GET /auth/profile
Get current user profile
- **Access**: Private
- **Response**: User profile data

### PUT /auth/change-password
Change user password
- **Access**: Private
- **Body**: `{ current_password, new_password }`
- **Response**: Success message

### POST /auth/verify-token
Verify JWT token validity
- **Access**: Private
- **Response**: Token validation status

---

## 👥 Profile Endpoints

### GET /profiles
Get all profiles with pagination and search
- **Access**: Private
- **Query Params**: 
  - `page` (default: 1)
  - `limit` (default: 10)
  - `search` (optional)
- **Response**: Paginated list of profiles with checkup counts

### POST /profiles
Create new profile
- **Access**: Private
- **Body**: 
  ```json
  {
    "nama": "string",
    "usia": "number",
    "alamat": "string",
    "no_telepon": "string (optional)",
    "kontak_darurat": "string (optional)",
    "riwayat_penyakit": "string (optional)",
    "obat_rutin": "string (optional)",
    "alergi": "string (optional)",
    "checkup_data": "object (optional)"
  }
  ```
- **Response**: Created profile with QR code

### GET /profiles/:id
Get specific profile by ID
- **Access**: Private
- **Response**: Profile details with recent checkups

### PUT /profiles/:id
Update profile
- **Access**: Private
- **Body**: Partial profile data to update
- **Response**: Updated profile data

### DELETE /profiles/:id
Delete profile
- **Access**: Private
- **Response**: Success message

### GET /profiles/:id/qr
Get profile QR code
- **Access**: Private
- **Response**: QR code data and image

### POST /profiles/scan
Scan QR code to get profile
- **Access**: Private
- **Body**: `{ qr_data }`
- **Response**: Profile data and recent checkups

---

## 🩺 Checkup Endpoints

### GET /checkups
Get all checkups with pagination
- **Access**: Private
- **Query Params**:
  - `page` (default: 1)
  - `limit` (default: 20)
  - `profile_id` (optional filter)
- **Response**: Paginated list of checkups with profile names

### POST /checkups
Create new checkup
- **Access**: Private
- **Body**:
  ```json
  {
    "profile_id": "number",
    "tekanan_darah": "string",
    "gula_darah": "number",
    "berat_badan": "number (optional)",
    "tinggi_badan": "number (optional)",
    "keluhan": "string (optional)",
    "tanggal": "date",
    "catatan": "string (optional)"
  }
  ```
- **Response**: Created checkup data

### GET /checkups/:id
Get specific checkup by ID
- **Access**: Private
- **Response**: Checkup details with profile information

### PUT /checkups/:id
Update checkup
- **Access**: Private
- **Body**: Partial checkup data to update
- **Response**: Updated checkup data

### DELETE /checkups/:id
Delete checkup
- **Access**: Private
- **Response**: Success message

---

## 📊 Dashboard Endpoints

### GET /dashboard/stats
Get dashboard statistics
- **Access**: Private
- **Response**: 
  ```json
  {
    "overview": {
      "totalProfiles": "number",
      "todayCheckups": "number", 
      "monthlyCheckups": "number",
      "averageAge": "number"
    },
    "healthStats": {
      "avgGulaDarah": "number",
      "highSugarCount": "number",
      "lowSugarCount": "number"
    },
    "recentActivities": "array",
    "ageDistribution": "array",
    "monthlyTrends": "array"
  }
  ```

### GET /dashboard/alerts
Get health alerts and warnings
- **Access**: Private
- **Response**: 
  ```json
  {
    "alerts": "array of alert objects",
    "summary": {
      "highSugar": "number",
      "lowSugar": "number", 
      "noRecentCheckup": "number",
      "total": "number"
    }
  }
  ```

### GET /dashboard/reports
Get health reports and analytics
- **Access**: Private
- **Query Params**: `period` (week|month|quarter|year, default: month)
- **Response**: 
  ```json
  {
    "period": "string",
    "sugarTrends": "array",
    "bloodPressureAnalysis": "array",
    "profilePerformance": "array"
  }
  ```

---

## 📈 Additional Endpoints (Client-side defined)

### Analytics Endpoints
- `GET /analytics/trends/:profileId` - Health trends for specific profile
- `GET /analytics/alerts/:profileId` - Health alerts for specific profile
- `GET /analytics/alerts` - All health alerts

### Reports Endpoints
- `POST /reports/generate` - Generate reports (PDF/Excel)

### Profile Charts
- `GET /profiles/:id/charts` - Get profile health charts

---

## 🚨 Error Handling

All endpoints return standardized error responses:
```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error (in development)"
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `429` - Too Many Requests (Rate Limited)
- `500` - Internal Server Error

---

## 🔧 Testing

Run the API endpoint tests:
```bash
node test-api-endpoints.js
```

This will test all endpoints and provide a comprehensive report of API functionality.

---

## 📝 Notes

1. All timestamps are in ISO 8601 format
2. Pagination uses 1-based indexing
3. Search is case-insensitive and searches across multiple fields
4. QR codes are automatically generated for new profiles
5. Authentication tokens expire after 24 hours
6. Rate limiting is applied to sensitive operations
7. All data is validated before processing
8. Database transactions ensure data consistency
